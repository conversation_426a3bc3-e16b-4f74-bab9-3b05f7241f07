WHATSAPP_LOGGER_TESTS_CODE_COMPONENT_ID = 298

from logger_local.LoggerComponentEnum import LoggerComponentEnum


class FieldLocalTestsConstants:
    FIELD_LOCAL_COMPONENT_ID = 1345423523
    FIELD_LOCAL_COMPONENT_NAME = "Event Tests"
    FIELD_LOCAL_DEVELOPER_EMAIL = "<EMAIL>"

    FIELD_LOCAL_TEST_LOGGER_OBJECT = {
        'componentId': FieldTestsConstants.FIELD_LOCAL_COMPONENT_ID,
        'componentName': FieldTestsConstants.FIELD_LOCAL_COMPONENT_NAME,
        'componentCategory': LoggerComponentEnum.ComponentCategory.Unit_Test.value,
        # TODO Please add the framework you use
        'testingFramework': LoggerComponentEnum.testingFramework.pytest.value,
        'developerEmailAddress': FieldTestsConstants.FIELD_LOCAL_DEVELOPER_EMAIL
    }
