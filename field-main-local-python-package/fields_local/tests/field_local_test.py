# So we can run debugger without launch.json in VSCode
from dotenv import load_dotenv
load_dotenv()

# TODO: This is an example file which you should rename to a filename which includes the class name and the words "test"
import pytest
import sys

# from database_mysql_local.generic_crud import GenericCRUD
from logger_local.LoggerLocal import Logger

from ..src.field_local import FieldLocal as Field
# TODO Replace FIELDS_LOCAL to i.e. EVENT_LOCAL
# TODO Replace FieldsLocal to i.e. Event
from constants_tests_field_local import FIELD_LOCAL_TEST_LOGGER_OBJECT


# TODO Do not use Magic Numbers, please bring the test_*_id from the relevant package.
#   Fields:
#       TEST_API_TYPE_ID = ApisLocal.get_test_api_id()
#       TEST_PROFILE_ID = ProfilesLocal.get_test_profile_id()

logger = Logger.create_logger(object=FIELD_LOCAL_TEST_LOGGER_OBJECT)


def test_something1():
    logger.start("test some test")
    example = Field(is_test_data=True)
    print(example)
    # TODO: implement your test using pytest
    logger.end("test successful")


def test_somethings2():
    logger.start("test some test")
    example = Field(is_test_data=True)
    print(example)
    logger.end("test successful")


def test_something3():
    logger.start("test some test")
    example = Field(is_test_data=True)
    print(example)
    logger.end("test successful")


# If we use only a call to some_test(), this can cause unexpected behaviour, for example
# it can cause the test to be run multiple times simultaneously. This is why we use the following
# Which cause the test to be run only once with pytest:
if __name__ == "__main__":
    pytest.main(sys.argv[1:])


# You can also run only specific test functions by using the following command:
# This example will run only some_test and some_test3
# Mark and click `ctrl + /` to uncomment the following code
# if __name__ == "__main__":
#     # 'directory-with-the-same-name-as-the-repo/package_name_until_including_local_or_remote/tests/examples_local_test.py' is the relative path to the test file
#     # 'some_test' and 'some_test3' are the names of the test functions we want to run
#     pytest.main(
#         ['directory-with-the-same-name-as-the-repo/package_name_until_including_local_or_remote/tests/examples_local_test.py::some_test',
#          'directory-with-the-same-name-as-the-repo/package_name_until_including_local_or_remote/tests/examples_local_test.py::some_test3']
#     )
