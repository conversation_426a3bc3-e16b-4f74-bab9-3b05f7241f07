# from src.event_constants_src import <EXAMPLE>_COMPONENT_ID, <EXAMPLE>_COMPONENT_NAME, \
#     DEVELOPER_EMAIL
from logger_local.LoggerComponentEnum import LoggerComponentEnum


class FieldsLocalTestsConstants:
    <EXAMPLE>_COMPONENT_ID = 1345423523
    <EXAMPLE>_COMPONENT_NAME = "<Field> Tests"
    DEVELOPER_EMAIL = "<EMAIL>"


<EXAMPLE>S_LOCAL_TEST_LOGGER_OBJECT = {
    'component_id': FieldsLocalTestsConstants.<EXAMPLE>_COMPONENT_ID,
    'component_name': FieldsLocalTestsConstants.<EXAMPLE>_COMPONENT_NAME,
    'component_category': LoggerComponentEnum.ComponentCategory.Unit_Test.value,
    'testing_framework': LoggerComponentEnum.testingFramework.pytest.value,
    'developer_email': FieldsLocalTestsConstants.DEVELOPER_EMAIL
}
