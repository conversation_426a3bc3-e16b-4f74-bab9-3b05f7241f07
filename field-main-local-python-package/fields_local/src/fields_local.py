# TODO: This is an example file which you should delete/edit after implementing
# from database_mysql_local.generic_crud import GenericCRUD
from logger_local.MetaLogger import <PERSON><PERSON><PERSON>og<PERSON>, Logger
from python_sdk_remote.utilities import our_get_env

# TODO Replace FIELDS_LOCAL to i.e. EVENTS_LOCAL
from .examples_local_constants_src import (FIELDS_LOCAL_CODE_LOGGER_OBJECT,
                               FieldsLocal_SCHEMA_NAME, FieldsLocal_TABLE_NAME,
                               FieldsLocal_VIEW_NAME, FieldsLocal_COLUMN_NAME)

logger = Logger.create_logger(object=FieldsLocalS_LOCAL_CODE_LOGGER_OBJECT)


class FieldsLocal(GenericCRUD, metaclass=MetaLogger,
                   object=FieldsLocalConstantsFIELDS_LOCAL_CODE_LOGGER_OBJECT):
    def __init__(self, is_test_data: bool = False) -> None:
        GenericCRUD.__init__(self,
                             default_schema_name=FieldsLocalConstants.FieldsLocal_SCHEMA_NAME,
                             default_table_name=FieldsLocalConstants.FieldsLocal_TABLE_NAME,
                             default_view_table_name=FieldsLocalConstants.FieldsLocal_VIEW_NAME,
                             default_column_name=FieldsLocalConstants.FieldsLocal_COLUMN_NAME,
                             is_test_data=is_test_data)

    # If this is CRUD method please make sure we don't have this method
    #  functionality already in GenericCRUD and if needed wrap the GenericCRUD
    #  method
    def some_function(self, arg1: int, arg2: str) -> str:
        # TODO: Implement
        self.logger.info("whatever you want to log")
        return_value = "some value"
        return return_value  # use return <variable> and not return <statement>

# TODO: once done, run some auto lint formatter such as autopep8, autoflake or
#  whatever your IDE provide.
