# devDependencies (packages for development/testing), production/runtime dependencies in setup.py
# TODO: add the packages that your package depends on here and in setup.py
#   (everything listed in the imports)

# In local-package uncomment the bellow line
# smart-datastore-local

logger-local # https://pypi.org/project/logger-local/
python-sdk-remote

# Requirements for Tests/Testing (Do not add to setup.py install_requires)
pytest # https://pypi.org/project/pytest/

pycallgraph2
