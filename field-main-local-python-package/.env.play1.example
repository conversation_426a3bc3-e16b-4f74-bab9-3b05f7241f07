# This is the .env file for play1 environment in the project directory and not in the root directory
# Please do not delete/rename this file

BRAND_NAME=Circlez
ENVIRONMENT_NAME=play1

# TODO In -local-package we need RDS environment variables, in -remote-package please delete the RDS variables from this file
RDS_HOSTNAME=db.play1.circ.zone
# TODO: Please update your RDS_USERNAME and RDS_PASSWORD
# RDS_USERNAME withour the @circ.zone
RDS_USERNAME=tal.r
RDS_PASSWORD=

# TODO In -local-package we need JWT_SECRET_KEY environment variables, in -remote-package please delete the JWT_SECRET_KEY variables from this file
JWT_SECRET_KEY=4rehrkj43h5lkefkdslcw4r

LOGZIO_TOKEN=cXNHuVkkffkilnkKzZlWExECRlSKqopE

# Needed by the logger-local
# TODO: Please update you username in the product i.e. <EMAIL> and the password in the product
PRODUCT_USER_IDENTIFIER=
PRODUCT_PASSWORD=


# AWS (in case of Local you need AWS environment variables, in case of remote AWS environment variables are not needed)

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=

AWS_DEFAULT_STORAGE_BUCKET_NAME="storage.us-east-1.play1.circ.zone"

AWS_DEFAULT_REGION=us-east-1