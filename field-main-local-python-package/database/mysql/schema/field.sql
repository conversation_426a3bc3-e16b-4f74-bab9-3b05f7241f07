CREATE TABLE `field_table` (
  `field_id` smallint unsigned NOT NULL AUTO_INCREMENT,
  `number` bigint unsigned DEFAULT NULL,
  `identifier` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL COMMENT 'In use by CSVTpContactPersonProfile.py\nMaybe in use by Text Block and Templates?',
  `description` text,
  `variable_id` int unsigned DEFAULT NULL COMMENT 'I think we should remove this field and use variable_table.field_id',
  `is_ml_table` tinyint DEFAULT NULL COMMENT 'Should change to is_ml_field',
  `table_id` smallint unsigned DEFAULT NULL,
  `database_field_name` varchar(45) DEFAULT NULL COMMENT 'No unique i.e. Twitter and LinkedIn handle',
  `database_sub_field_name` varchar(45) DEFAULT NULL,
  `database_sub_field_value` varchar(45) DEFAULT NULL,
  `processing_id` varchar(45) DEFAULT NULL,
  `processing_database_field_name` varchar(45) DEFAULT NULL,
  `internal_description` varchar(255) DEFAULT NULL,
  `is_test_data` tinyint DEFAULT NULL,
  `start_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_timestamp` timestamp NULL DEFAULT NULL,
  `created_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_user_id` bigint unsigned NOT NULL COMMENT 'TODO We need to remove this',
  `created_real_user_id` bigint unsigned NOT NULL,
  `created_effective_user_id` bigint unsigned NOT NULL,
  `created_effective_profile_id` bigint unsigned NOT NULL,
  `updated_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_user_id` bigint unsigned NOT NULL COMMENT 'TODO We need to remove this',
  `updated_real_user_id` bigint unsigned NOT NULL,
  `updated_effective_user_id` bigint unsigned NOT NULL,
  `updated_effective_profile_id` bigint unsigned NOT NULL,
  `visibility_id` tinyint DEFAULT '1',
  PRIMARY KEY (`field_id`),
  UNIQUE KEY `id_UNIQUE` (`field_id`),
  UNIQUE KEY `idx_field_name` (`name`),
  KEY `field_table.variable_id_variable_table_idx` (`variable_id`),
  KEY `table_id_idx` (`table_id`),
  CONSTRAINT `field_table.variable_id_variable_table` FOREIGN KEY (`variable_id`) REFERENCES `variable_table` (`variable_id`),
  CONSTRAINT `table_id` FOREIGN KEY (`table_id`) REFERENCES `database`.`table_definition_table` (`table_definition_id`)
) ENGINE=InnoDB AUTO_INCREMENT=65 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='The assumption is that field can be only in one field. Is it correct?';
