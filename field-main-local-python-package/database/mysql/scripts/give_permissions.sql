-- TODO If you need permissions to run this package tests, please update the file /database/mysql/scripts/give_permissions.sql in your repo
-- Please ask only for what you need to complete your task.
-- We don't give all privileges. Please don't hesitate to ask if there's anything you need to make the code run.
-- Please ask your Team Lead to run it for you in play1, dvlp1, prod1 ...
-- Field:
-- https://github.com/circles-zone/python-package-template/blob/dev/replace-with-repo-name-push-and-merge/database/mysql/scripts/give_permissions.sql

-- SET @username = 'zvi.n';
-- SELECT @username;
-- USE contact_note;
-- SET @a=CONCAT("'",@username,"@\'%\'\'");
-- SELECT @a;



-- Please add this paragraph per each schema you need permissions to.
-- Please give the right permissions you need.
-- SET @query = CONCAT("GRANT SELECT, INSERT, UPDATE ON `location`.* TO ", @user_role,";");

-- In case of local package you might need SELECT, INSERT, UPDATE (to run the tests)
-- In case of remote package you need only SELECT for viewing the values with Workbench (not for runining the tests)


-- TODO Please put your username here
SET @user_role = "'tal.r'@'%'";

-- For example, if you want to access component_table, component_view ... you should fill-in 'component' in the second parameter
CALL grant_permissions(@user_role, 'component', 'SELECT');

-- Only if you need more than SELECT, please add the permissions you need
CALL grant_permissions(@user_role, 'component', 'INSERT, UPDATE, SELECT');
