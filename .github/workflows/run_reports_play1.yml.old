name: Execute SQL Files (Create VIEWs, Report) in <project-name> Python Package play1
on:
  push:
    branches: [ "BU-*" ] # We can't remove this as dev branch is blocked from deploying to play1
    paths:
      - reports/**
  pull_request:
    branches: [ dev ]
    paths:
      - reports/**

env:
  brand_name: Circlez
  environment_name: play1
  # TODO: repo_name same as repo name, but with underscore instead of hypean/minus/dash as the main directory/folder
  # TODO: Make sure the /src and /tests directories are under the directory with the same name as the repo called the project directory i.e. /<project-name>/src We should create/update the directory name to be the same as the repo name i.e. /time_local_python_package (with underscore). In the root directory, we should have only .gitignore
  repo_name: <repo-name-with-hypen> # TODO i.e. unified_json_api_python_package
  package_name: <package-name-with-underlines> # TODO i.e. unified_json_api or variable_local
    
jobs:
  Execute-SQL:
    runs-on: ubuntu-latest
    environment: play1
    steps:
    - uses: actions/checkout@v3
      
    # This step is independent and can be executed in a separate job
    - name: Run the reports
      run: |
        echo "List of reports"
        cd ./$repo_name/$package_name
        ls ./reports/*.sql
        echo "Pipeline all *.sql to MySQL"
        cat ./reports/*.sql | mysql -u ${{ vars.READ_ONLY_RDS_USERNAME_PLAY1 }} -p"${{ secrets.READ_ONLY_RDS_PASSWORD_PLAY1 }}" -h ${{ vars.RDS_HOSTNAME_PLAY1 }}
