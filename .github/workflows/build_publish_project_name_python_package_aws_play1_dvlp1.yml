# Being used in https://github.com/circles-zone/<repo-name>/edit/BU-2241--develop-<repo-name>/.github/workflows/build_publish_contact_email_address_local_python_package.yml


name: Build Publish <repo-name> to PyPI.org play1 dvlp1
on:
  push:
    branches: [ "BU-*", dev ]
  # TODO We prefer to run the GHA only on Pull Requests and not on every Push, but we can't as we use the same YML file for all environments using "if: startsWith(github.ref, 'refs/heads/bu-')"
  #pull_request:
    #branches: [ dev ]
jobs:
  # play1 Build, Test & Publish
  publish-repo-name-play1:
    name: <repo-short-name>P()Play1 Build, Test & Publish Python Package
    # TODO When using this "if: startsWith(github.ref, 'refs/heads/bu-')", unfortunately, we can't use "on: pull_request:" - possible solutions 1. change the if 2. split GHA per environment
    if: startsWith(github.ref, 'refs/heads/bu-')
    #if: '! github.event.pull_request.draft'
    strategy:
      # This should be commented/true, when it is equal to false, the job can be Green but in Summary not Green
      #fail-fast: false
      matrix:
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    permissions:
      id-token: write      # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write      # To allow the Reuseable GitHub Action (i.e.  For publish_python_package.yml) to push the file created/updated by Sql2Code
      pull-requests: write # To enable the Reusable GitHub Action (i.e.  For publish_python_package.yml) to create a Pull Request (PR) following Sql2Code
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: <repo-name>
      # Not needed for publish_ needed for run_
      #branch-name: dev
      repo-directory: <repo-directory>
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging can be changed to 1, debug, info ...
      #logger-minimum-severity: error
      #LOGGER_CONFIGURATION_JSON_PATH='contact-group-local-python-package\\.vscode\\logger_configuration.json'
      #TODO Only for debugging can be changed temporarily to true
      #logger-is-write-to-sql: false
      is-run-local: true
      #is-run-remote: true


  ## play1 Sanity Tests
  run-sanity-repo-name-play1-dev:
    name: <sanity-repo-short-name>P(dev)Play1 Run
    #if: contains(github.event.head_commit.message, '[run sanity]')
    #if: '! github.event.pull_request.draft'
    # This doesn't work with "on; pull_reqest:"
    #if: startsWith(github.ref, 'refs/heads/bu-')
    if: false
    #needs: publish-<repo-name>-play1
    strategy:
      fail-fast: false
        #matrix:
      #target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      #environment-name: ${{ matrix.target-environments }}
      repo-name: <sanity-repo-name>
      #branch-name: dev
      repo-directory: <sanity-repo-directory>
      #package-directory: <sanity-package-directory>
      #LOGGER_CONFIGURATION_JSON_PATH='contact-group-local-python-package\\.vscode\\logger_configuration.json'
      is-run-local: true
      #is-run-remote: true


  run-sanity-<repo-name>-play1-sanity-branch-jira-number:
    name: <sanity-repo-short-name>P(<sanity-branch-jira-number>)Play1 Run
    #if: startsWith(github.ref, 'refs/heads/bu-')
    if: false
    #needs: publish-<repo-name>-play1
    strategy:
      fail-fast: false
        #matrix:
      #target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      #environment-name: ${{ matrix.target-environments }}
      repo-name: <sanity-repo-name>
      branch-name: <sanity-branch-name>
      repo-directory: <sanity-repo-directory>
      package-directory: <sanity-package-directory>
      #LOGGER_CONFIGURATION_JSON_PATH='contact-group-local-python-package\\.vscode\\logger_configuration.json'
      is-run-local: true
      #is-run-remote: true


  # dvlp1 Build, Test, and Publish
  # No need to publish in dvlp1, only to run
  # Is it enough to use run? No, if we want to publish the artifacts somewhere else
  publish-repo-name-dvlp1-dev:
    name: <repo-short-name>P(dev)Dvlp1 Build, Test & Publish
    if: github.ref == 'refs/heads/dev'
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    permissions:
      id-token: write      # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write      # Required by publish_python_package.yml
      pull-requests: write # Required by publish_python_package.yml
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    secrets: inherit
    with:
      brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: <repo-name>
      repo-directory: <repo-directory>
      is-run-local: true
      #is-run-remote: true


  # TODO As soon as other repo is using this repo uncomment this
  # run-<sanity-repo-name>-dvlp1-dev:
  #   name: <sanity-repo-short-name>P(dev)Dvlp1 Run
  #   #if: github.ref == 'refs/heads/dev'
  #   if: false
  #   #needs: publish-<repo-name>-dvlp1-dev
  #   strategy:
  #     matrix:
  #       target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
  #   uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
  #   secrets: inherit
  #   with:
  #     #brand-name: Circlez
  #     environment-name: ${{ matrix.target-environments }}
  #     repo-name: <sanity-repo-name>
  #     branch-name: dev
  #     repo-directory: <sanity-repo-directory>
  #     is-run-local: true
  #     #is-run-remote: true
