{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Current File",
            "type": "debugpy", // python -> debugpy
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name":"Python Debugger: Current File",
            "type":"debugpy",
            "request":"launch",
            "program":"${file}",
            "cwd": "${workspaceFolder}/criteria-local-python-package/criteria_local",
            "console":"integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}/criteria-local-python-package/criteria_local" // src ...
            },
        },
        {
            "name": "Python Debugger: Current File with Arguments",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "cwd": "${fileDirname}/criteria-local-python-package",
            "args": [
                "${command:pickArgs}"
            ]
        }
    ],
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "autopep8"
}
